import React from "react";
import "./App.css";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Layout from "./Components/Global/Layout.jsx";
import InpatientVisit from "./Pages/InpatientVisit/index.jsx";
import DoctorsAppointment from "./Pages/DoctorsAppointment/index.jsx";
import WalkInVisit from "./Components/Observation/AddWatchlistForm.jsx";
import "./Screen.js";
import Observation from "./Pages/Observation/Index.jsx";
import AddWatchlistForm from "./Components/Observation/AddWatchlistForm.jsx";
import WatchListDetails from "./Pages/Facility/details.jsx";
import Facility from "./Pages/Facility/Index.jsx";
import ObservationDetails from "./Pages/Observation/ObservationDetails.jsx";
import PatientHub from "./Pages/PatientHub";
import ParientDetails from "./Pages/PatientHub/ParientDetails.jsx";
import UserGroup from "./Pages/UserManagement/UserGroup/index.jsx";
import User from "./Pages/UserManagement/User/index.jsx";
import UserDetails from "./Pages/UserManagement/User/UserDetails.jsx";
import AccessArea from "./Pages/AccessArea/AccessArea.jsx";
import AccessAreaetails from "./Pages/AccessArea/AccessAreaetails.jsx";
import AccessAreaGroup from "./Pages/AccessAreaGroup/AccessAreaGroup.jsx";
import AccessAreaGroupDetails from "./Pages/AccessAreaGroup/AccessAreaGroupDetails.jsx";
import Profile from "./Pages/Profile/Profile.jsx";
import AddAccessGroupForm from "./Components/AccessAreaGroup/AddAccessGroupForm.jsx";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ProtectedRoute from "./utils/ProtectedRoute.jsx";
import Login from "./Pages/Login.jsx";
import UnprotectedRoute from "./utils/UnprotectedRoute.jsx";
import IdentityHub from "./Pages/IdentityHub/index.jsx";
import IdentityDetails from "./Pages/IdentityHub/details.jsx";
import IdentityHubAdd from "./Components/IdentityHub/IdentityHubAdd.jsx";
import ValidationTask from "./Pages/ValidationTask/Index.jsx";
import Details from "./Pages/ValidationTask/Details.jsx";
import ValidationRun from "./Pages/ValidationRun/index.jsx";
import ValidationConfiguration from "./Pages/ValidationConfiguration/index.jsx";
import AddConfigurationForm from "./Components/ValidationTask/AddForm.jsx";
import RequestHub from "./Pages/RequestHub/index.jsx";
import TaskHub from "./Pages/TaskHub/index.jsx";
// import VisitorHub from "./Pages/VisitorsHub/index.jsx";
import TemporaryCards from "./Pages/Temporary/Temporary-Cards.jsx";
// import BadgingIndex from "./Pages/Credential/Index.jsx";
// import BadgingDetails from "./Pages/Credential/Details.jsx";
// import MyVisits from "./Pages/MyApp/MyEvents.jsx";
import CredentialHub from "./Pages/Credential/Index.jsx";
import CredentialDetails from "./Pages/Credential/Details.jsx";
import Guest from "./Pages/MyApp/Guest.jsx";
import MyProfile from "./Pages/MyApp/MyProfile.jsx";
// import MyTeam from "./Pages/MyApp/MyStaff.jsx";
import TeamDetails from "./Pages/MyApp/TeamDetails.jsx";
import Request from "./Pages/MyApp/Request.jsx";
import MyAudits from "./Pages/MyApp/MyAudits.jsx";
// import RemoveAccessRequest from "./Components/MyApp/MyRequest/RemoveAccessRequest.jsx";
import Task from "./Pages/MyApp/Task.jsx";
import MyAccessAreas from "./Pages/MyApp/MyAccessAreas.jsx";
import Home from "./Pages/Home.jsx";
import ReceptionDesk from "./Pages/VisitorsHub/index.jsx";
import MyEvents from "./Pages/MyApp/MyEvents.jsx";
import MyStaff from "./Pages/MyApp/MyStaff.jsx";
import IDDesigner from "./Components/IDDesigner/DesignerContainer.js";

const App = () => {

  return (
    <Router>
      {/* Add the ToastContainer component here */}
      <ToastContainer />
      <Routes>
        {/* Unprotected Routes */}
        <Route element={<UnprotectedRoute />}>
          <Route path="/login" element={<Login />} />
        </Route>

        {/* Protected Routes */}
        <Route element={<ProtectedRoute />}>
          <Route path="/" element={<Layout />}>
            <Route index element={<InpatientVisit />} />
            <Route path="/doctors-appointment" element={<DoctorsAppointment />} />
            <Route path="/reception-Desk" element={<ReceptionDesk />}/>
            <Route path="/observation-roster" element={<Observation />} />
            <Route path="/walk-in" element={<WalkInVisit />} />
            <Route path="/details" element={<ObservationDetails />} />
            <Route path="/add-observation-form" element={<AddWatchlistForm />} />
            <Route path="/facility" element={<Facility />} />
            <Route path="/facility/:facilityId" element={<WatchListDetails tab="Facility" />} />
            <Route path="/patient-hub" element={<PatientHub />} />
            <Route path="/patient-details/:patientId" element={<ParientDetails />} />
            <Route path="/user" element={<User />} />
            <Route path="/access-areas" element={<AccessArea />} />
            <Route path="/user-details" element={<UserDetails />} />
            <Route path="/access-details" element={<AccessAreaetails />} />
            <Route path="/access-group" element={<AccessAreaGroup />} />
            <Route path="/identity-hub" element={<IdentityHub />} />
            <Route path="/identity-details" element={<IdentityDetails />} />
            <Route path="/access-group-details" element={<AccessAreaGroupDetails />} />
            <Route path="/user-group" element={<UserGroup />} />
            <Route path="/validation-task" element={<ValidationTask />} />
            <Route path="/validation-details" element={<Details />} />
            <Route path="/validation-run" element={<ValidationRun />} />
            <Route path="/validation-configuration" element={<ValidationConfiguration />} />
            <Route path="/add-configuration" element={<AddConfigurationForm />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/add-access-group-form" element={<AddAccessGroupForm />} />
            <Route path="/add-Identity" element={<IdentityHubAdd />} />
            <Route path="/request-hub"element={<RequestHub /> }/>
            <Route path="/task-hub"element={<TaskHub />} />
            <Route path="/temporary-cards"element={<TemporaryCards />} />
            <Route path="/credential"element={<CredentialHub />} />
            <Route path="/credential-details"element={<CredentialDetails />} />
            <Route path="/My-Events"element={<MyEvents />} />
            <Route path="/my-guests"element={<Guest />} />
            <Route path="/my-profile"element={<MyProfile />} />
            <Route path="/my-staff"element={<MyStaff />} />
            <Route path="/team-details"element={<TeamDetails />} />
            <Route path="/request"element={<Request />} />
            <Route path="/my-audits"element={<MyAudits />} />
            <Route path="/my-task"element={<Task />} />
            <Route path="/my-areas"element={<MyAccessAreas/>} />
            <Route path="/home"element={<Home/>} />
            <Route path="/id-designer"element={<IDDesigner/>} />
            {/* <Route path="/remove-request"element={<RemoveAccessRequest />} /> */}
          </Route>
        </Route>
      </Routes>
    </Router>
  );
};

export default App;
