import React, { useRef, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Stage, Layer, Rect, Line } from 'react-konva';
import { Box } from '@mui/material';

import DesignElement from './Elements/DesignElement';
import SelectionBox from './SelectionBox';

import {
  updateElement,
  selectElement,
  selectMultipleElements,
  clearSelection,
  setCanvasOffset,
} from '../../redux/idDesignerSlice';

const DesignCanvas = () => {
  const dispatch = useDispatch();
  const stageRef = useRef();
  const [stageSize, setStageSize] = useState({ width: 800, height: 600 });
  const [selectionRect, setSelectionRect] = useState(null);
  const [isDragging, setIsDragging] = useState(false);

  const {
    elements,
    selectedElementIds,
    canvasConfig,
    previewMode,
  } = useSelector((state) => state.idDesigner);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const container = stageRef.current?.container();
      if (container) {
        const containerRect = container.getBoundingClientRect();
        setStageSize({
          width: containerRect.width,
          height: containerRect.height,
        });
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Grid lines
  const renderGrid = () => {
    if (!canvasConfig.showGrid) return null;

    const lines = [];
    const { gridSize, width, height } = canvasConfig;
    const { scale, offsetX, offsetY } = canvasConfig;

    // Vertical lines
    for (let i = 0; i <= width / gridSize; i++) {
      const x = i * gridSize * scale + offsetX;
      if (x >= 0 && x <= stageSize.width) {
        lines.push(
          <Line
            key={`v-${i}`}
            points={[x, 0, x, stageSize.height]}
            stroke="#e0e0e0"
            strokeWidth={0.5}
            listening={false}
          />
        );
      }
    }

    // Horizontal lines
    for (let i = 0; i <= height / gridSize; i++) {
      const y = i * gridSize * scale + offsetY;
      if (y >= 0 && y <= stageSize.height) {
        lines.push(
          <Line
            key={`h-${i}`}
            points={[0, y, stageSize.width, y]}
            stroke="#e0e0e0"
            strokeWidth={0.5}
            listening={false}
          />
        );
      }
    }

    return lines;
  };

  // Canvas background
  const renderCanvasBackground = () => {
    const { width, height, background, scale, offsetX, offsetY } = canvasConfig;
    
    return (
      <Rect
        x={offsetX}
        y={offsetY}
        width={width * scale}
        height={height * scale}
        fill={background}
        stroke="#ccc"
        strokeWidth={1}
        listening={false}
      />
    );
  };

  // Handle stage click
  const handleStageClick = (e) => {
    if (e.target === e.target.getStage()) {
      dispatch(clearSelection());
    }
  };

  // Handle element drag
  const handleElementDragMove = (elementId, newPos) => {
    const { snapToGrid, gridSize, scale } = canvasConfig;
    
    let { x, y } = newPos;
    
    if (snapToGrid) {
      x = Math.round(x / (gridSize * scale)) * (gridSize * scale);
      y = Math.round(y / (gridSize * scale)) * (gridSize * scale);
    }

    dispatch(updateElement({
      id: elementId,
      properties: { x: x / scale, y: y / scale },
    }));
  };

  // Handle element selection
  const handleElementClick = (elementId, e) => {
    e.cancelBubble = true;
    const multiSelect = e.evt.shiftKey || e.evt.ctrlKey || e.evt.metaKey;
    dispatch(selectElement({ id: elementId, multiSelect }));
  };

  // Handle mouse down for selection rectangle
  const handleMouseDown = (e) => {
    if (e.target !== e.target.getStage()) return;
    
    const pos = e.target.getStage().getPointerPosition();
    setSelectionRect({
      x: pos.x,
      y: pos.y,
      width: 0,
      height: 0,
    });
    setIsDragging(true);
  };

  // Handle mouse move for selection rectangle
  const handleMouseMove = (e) => {
    if (!isDragging || !selectionRect) return;

    const pos = e.target.getStage().getPointerPosition();
    setSelectionRect({
      ...selectionRect,
      width: pos.x - selectionRect.x,
      height: pos.y - selectionRect.y,
    });
  };

  // Handle mouse up for selection rectangle
  const handleMouseUp = () => {
    if (!isDragging || !selectionRect) return;

    setIsDragging(false);

    // Find elements within selection rectangle
    const { scale, offsetX, offsetY } = canvasConfig;
    const selectionBounds = {
      x: Math.min(selectionRect.x, selectionRect.x + selectionRect.width) - offsetX,
      y: Math.min(selectionRect.y, selectionRect.y + selectionRect.height) - offsetY,
      width: Math.abs(selectionRect.width),
      height: Math.abs(selectionRect.height),
    };

    const selectedIds = elements
      .filter((element) => {
        const elementBounds = {
          x: element.x * scale,
          y: element.y * scale,
          width: element.width * scale,
          height: element.height * scale,
        };

        return (
          elementBounds.x >= selectionBounds.x &&
          elementBounds.y >= selectionBounds.y &&
          elementBounds.x + elementBounds.width <= selectionBounds.x + selectionBounds.width &&
          elementBounds.y + elementBounds.height <= selectionBounds.y + selectionBounds.height
        );
      })
      .map((element) => element.id);

    if (selectedIds.length > 0) {
      dispatch(selectMultipleElements(selectedIds));
    }

    setSelectionRect(null);
  };

  // Handle wheel for zoom
  const handleWheel = (e) => {
    e.evt.preventDefault();
    
    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    const newScale = e.evt.deltaY > 0 ? oldScale * 0.9 : oldScale * 1.1;
    const clampedScale = Math.max(0.1, Math.min(3, newScale));

    const newPos = {
      x: pointer.x - mousePointTo.x * clampedScale,
      y: pointer.y - mousePointTo.y * clampedScale,
    };

    stage.scale({ x: clampedScale, y: clampedScale });
    stage.position(newPos);
    stage.batchDraw();

    dispatch(setCanvasOffset({ x: newPos.x, y: newPos.y }));
  };

  return (
    <Box
      sx={{
        flex: 1,
        overflow: 'hidden',
        position: 'relative',
        cursor: isDragging ? 'crosshair' : 'default',
      }}
    >
      <Stage
        ref={stageRef}
        width={stageSize.width}
        height={stageSize.height}
        scaleX={canvasConfig.scale}
        scaleY={canvasConfig.scale}
        x={canvasConfig.offsetX}
        y={canvasConfig.offsetY}
        onClick={handleStageClick}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
        draggable={!isDragging}
      >
        <Layer>
          {/* Grid */}
          {renderGrid()}
          
          {/* Canvas Background */}
          {renderCanvasBackground()}
          
          {/* Design Elements */}
          {elements
            .sort((a, b) => a.zIndex - b.zIndex)
            .map((element) => (
              <DesignElement
                key={element.id}
                element={element}
                isSelected={selectedElementIds.includes(element.id)}
                isPreviewMode={previewMode}
                onDragMove={(newPos) => handleElementDragMove(element.id, newPos)}
                onClick={(e) => handleElementClick(element.id, e)}
              />
            ))}
          
          {/* Selection Rectangle */}
          {selectionRect && (
            <Rect
              x={selectionRect.x}
              y={selectionRect.y}
              width={selectionRect.width}
              height={selectionRect.height}
              fill="rgba(0, 123, 255, 0.1)"
              stroke="rgba(0, 123, 255, 0.5)"
              strokeWidth={1}
              listening={false}
            />
          )}
        </Layer>
      </Stage>

      {/* Selection Box for multi-select */}
      {selectedElementIds.length > 1 && (
        <SelectionBox
          elements={elements.filter(el => selectedElementIds.includes(el.id))}
          canvasConfig={canvasConfig}
        />
      )}
    </Box>
  );
};

export default DesignCanvas;
