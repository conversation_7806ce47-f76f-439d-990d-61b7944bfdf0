import React, { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Box, Paper, Toolbar, IconButton, Tooltip, Divider } from '@mui/material';
import {
  Undo as UndoIcon,
  Redo as RedoIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Visibility as PreviewIcon,
  Save as SaveIcon,
  FolderOpen as OpenIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

import DesignCanvas from './DesignCanvas';
import ToolPalette from './ToolPalette';
import PropertyPanel from './PropertyPanel';
import PreviewPane from './PreviewPane';
import TemplateManager from './TemplateManager';

import {
  undo,
  redo,
  setCanvasScale,
  togglePreviewMode,
  togglePropertyPanel,
  togglePreviewPane,
  toggleTemplateManager,
} from '../../redux/idDesignerSlice';

const DesignerContainer = () => {
  const dispatch = useDispatch();
  const {
    canvasConfig,
    history,
    previewMode,
    showPropertyPanel,
    showPreviewPane,
    showTemplateManager,
    currentTemplate,
  } = useSelector((state) => state.idDesigner);

  // Keyboard shortcuts
  const handleKeyDown = useCallback((event) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'z':
          event.preventDefault();
          if (event.shiftKey) {
            dispatch(redo());
          } else {
            dispatch(undo());
          }
          break;
        case 'y':
          event.preventDefault();
          dispatch(redo());
          break;
        case 's':
          event.preventDefault();
          // TODO: Implement save functionality
          break;
        case 'o':
          event.preventDefault();
          dispatch(toggleTemplateManager());
          break;
        default:
          break;
      }
    }
  }, [dispatch]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  const handleZoomIn = () => {
    const newScale = Math.min(canvasConfig.scale * 1.2, 3);
    dispatch(setCanvasScale(newScale));
  };

  const handleZoomOut = () => {
    const newScale = Math.max(canvasConfig.scale / 1.2, 0.1);
    dispatch(setCanvasScale(newScale));
  };

  const handleZoomReset = () => {
    dispatch(setCanvasScale(1));
  };

  return (
    <Box sx={{ height: 'calc(100vh - 120px)', display: 'flex', flexDirection: 'column', bgcolor: '#f5f5f5' }}>
      {/* Top Toolbar */}
      <Paper elevation={1} sx={{ zIndex: 1000 }}>
        <Toolbar variant="dense" sx={{ minHeight: 48, gap: 1 }}>
          {/* File Operations */}
          <Tooltip title="Open Template (Ctrl+O)">
            <IconButton
              size="small"
              onClick={() => dispatch(toggleTemplateManager())}
              color={showTemplateManager ? 'primary' : 'default'}
            >
              <OpenIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Save Template (Ctrl+S)">
            <IconButton size="small">
              <SaveIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* History Operations */}
          <Tooltip title="Undo (Ctrl+Z)">
            <IconButton
              size="small"
              onClick={() => dispatch(undo())}
              disabled={history.past.length === 0}
            >
              <UndoIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Redo (Ctrl+Y)">
            <IconButton
              size="small"
              onClick={() => dispatch(redo())}
              disabled={history.future.length === 0}
            >
              <RedoIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* Zoom Controls */}
          <Tooltip title="Zoom Out">
            <IconButton size="small" onClick={handleZoomOut}>
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>

          <Box
            sx={{
              minWidth: 60,
              textAlign: 'center',
              fontSize: '0.875rem',
              cursor: 'pointer',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              '&:hover': { bgcolor: 'action.hover' },
            }}
            onClick={handleZoomReset}
          >
            {Math.round(canvasConfig.scale * 100)}%
          </Box>

          <Tooltip title="Zoom In">
            <IconButton size="small" onClick={handleZoomIn}>
              <ZoomInIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* View Controls */}
          <Tooltip title="Toggle Preview Mode">
            <IconButton
              size="small"
              onClick={() => dispatch(togglePreviewMode())}
              color={previewMode ? 'primary' : 'default'}
            >
              <PreviewIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Toggle Property Panel">
            <IconButton
              size="small"
              onClick={() => dispatch(togglePropertyPanel())}
              color={showPropertyPanel ? 'primary' : 'default'}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>

          {/* Canvas Size Controls */}
          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
              {canvasConfig.width} × {canvasConfig.height}
            </Box>
          </Box>
        </Toolbar>
      </Paper>

      {/* Main Content Area */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* Left Sidebar - Tool Palette */}
        <Paper
          elevation={1}
          sx={{
            width: 280,
            display: 'flex',
            flexDirection: 'column',
            borderRadius: 0,
            borderRight: 1,
            borderColor: 'divider',
          }}
        >
          <ToolPalette />
        </Paper>

        {/* Center - Canvas Area */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <DesignCanvas />
        </Box>

        {/* Right Sidebar - Property Panel */}
        {showPropertyPanel && (
          <Paper
            elevation={1}
            sx={{
              width: 320,
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 0,
              borderLeft: 1,
              borderColor: 'divider',
            }}
          >
            <PropertyPanel />
          </Paper>
        )}

        {/* Preview Pane */}
        {showPreviewPane && (
          <Paper
            elevation={1}
            sx={{
              width: 400,
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 0,
              borderLeft: 1,
              borderColor: 'divider',
            }}
          >
            <PreviewPane />
          </Paper>
        )}
      </Box>

      {/* Template Manager Modal */}
      {showTemplateManager && <TemplateManager />}
    </Box>
  );
};

export default DesignerContainer;
