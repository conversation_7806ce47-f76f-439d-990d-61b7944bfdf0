import React, { forwardRef } from 'react';
import { Rect, Text, Group, Line } from 'react-konva';
import { useSelector } from 'react-redux';
import Mustache from 'mustache';

const BarcodeElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const { mockData } = useSelector((state) => state.idDesigner);

  const getBarcodeData = () => {
    if (isPreviewMode && element.data) {
      try {
        return Mustache.render(element.data, mockData);
      } catch (error) {
        console.warn('Error rendering barcode data template:', error);
        return element.data;
      }
    }
    return element.data || 'Barcode Data';
  };

  const renderBarcodeLines = () => {
    const lines = [];
    const lineCount = 20;
    const lineWidth = element.width / lineCount;
    
    for (let i = 0; i < lineCount; i++) {
      const x = i * lineWidth;
      const isWide = i % 3 === 0;
      lines.push(
        <Line
          key={i}
          points={[x, 0, x, element.height * 0.7]}
          stroke="#000"
          strokeWidth={isWide ? lineWidth * 0.8 : lineWidth * 0.4}
        />
      );
    }
    return lines;
  };

  return (
    <Group
      ref={ref}
      x={element.x}
      y={element.y}
      width={element.width}
      height={element.height}
      rotation={element.rotation || 0}
      opacity={element.opacity !== undefined ? element.opacity : 1}
      visible={element.visible !== false}
      draggable={!element.locked && !isPreviewMode}
      onClick={onClick}
      onDragEnd={onDragEnd}
    >
      {/* Background */}
      <Rect
        width={element.width}
        height={element.height}
        fill="#ffffff"
        stroke="#ccc"
        strokeWidth={1}
      />
      
      {/* Barcode lines */}
      <Group>
        {renderBarcodeLines()}
      </Group>
      
      {/* Data text */}
      <Text
        y={element.height * 0.75}
        width={element.width}
        height={element.height * 0.25}
        text={getBarcodeData()}
        fontSize={Math.min(10, element.height * 0.15)}
        fill="#000"
        align="center"
        verticalAlign="middle"
      />
    </Group>
  );
});

BarcodeElement.displayName = 'BarcodeElement';

export default BarcodeElement;
