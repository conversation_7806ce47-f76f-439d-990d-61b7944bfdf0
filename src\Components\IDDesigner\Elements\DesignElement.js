import React, { useRef, useEffect } from 'react';
import { Group, Transformer } from 'react-konva';
import { useSelector } from 'react-redux';

import TextElement from './TextElement';
import ImageElement from './ImageElement';
import QRCodeElement from './QRCodeElement';
import BarcodeElement from './BarcodeElement';
import ShapeElement from './ShapeElement';

const DesignElement = ({
  element,
  isSelected,
  isPreviewMode,
  onDragMove,
  onClick,
}) => {
  const transformerRef = useRef();
  const elementRef = useRef();
  
  const { canvasConfig } = useSelector((state) => state.idDesigner);

  useEffect(() => {
    if (isSelected && transformerRef.current && elementRef.current) {
      transformerRef.current.nodes([elementRef.current]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  const handleDragEnd = (e) => {
    const node = e.target;
    const newPos = {
      x: node.x(),
      y: node.y(),
    };
    onDragMove(newPos);
  };

  const handleTransformEnd = () => {
    const node = elementRef.current;
    if (!node) return;

    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    // Reset scale and apply to width/height
    node.scaleX(1);
    node.scaleY(1);

    const newAttrs = {
      x: node.x(),
      y: node.y(),
      width: Math.max(5, node.width() * scaleX),
      height: Math.max(5, node.height() * scaleY),
      rotation: node.rotation(),
    };

    // Update element through Redux
    onDragMove(newAttrs);
  };

  const renderElement = () => {
    switch (element.type) {
      case 'text':
        return (
          <TextElement
            ref={elementRef}
            element={element}
            isPreviewMode={isPreviewMode}
            onClick={onClick}
            onDragEnd={handleDragEnd}
          />
        );
      case 'image':
        return (
          <ImageElement
            ref={elementRef}
            element={element}
            isPreviewMode={isPreviewMode}
            onClick={onClick}
            onDragEnd={handleDragEnd}
          />
        );
      case 'qrcode':
        return (
          <QRCodeElement
            ref={elementRef}
            element={element}
            isPreviewMode={isPreviewMode}
            onClick={onClick}
            onDragEnd={handleDragEnd}
          />
        );
      case 'barcode':
        return (
          <BarcodeElement
            ref={elementRef}
            element={element}
            isPreviewMode={isPreviewMode}
            onClick={onClick}
            onDragEnd={handleDragEnd}
          />
        );
      case 'shape':
        return (
          <ShapeElement
            ref={elementRef}
            element={element}
            isPreviewMode={isPreviewMode}
            onClick={onClick}
            onDragEnd={handleDragEnd}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Group>
      {renderElement()}
      
      {isSelected && !isPreviewMode && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox, newBox) => {
            // Limit resize
            if (newBox.width < 5 || newBox.height < 5) {
              return oldBox;
            }
            return newBox;
          }}
          onTransformEnd={handleTransformEnd}
          enabledAnchors={[
            'top-left',
            'top-center',
            'top-right',
            'middle-right',
            'bottom-right',
            'bottom-center',
            'bottom-left',
            'middle-left',
          ]}
          rotateEnabled={true}
          rotateAnchorOffset={20}
          borderEnabled={true}
          borderStroke="#0066cc"
          borderStrokeWidth={2}
          borderDash={[4, 4]}
          anchorFill="#ffffff"
          anchorStroke="#0066cc"
          anchorStrokeWidth={2}
          anchorSize={8}
          anchorCornerRadius={4}
          rotateAnchorCursor="grab"
          centeredScaling={false}
          keepRatio={false}
        />
      )}
    </Group>
  );
};

export default DesignElement;
