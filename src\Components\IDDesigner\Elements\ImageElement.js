import React, { forwardRef } from 'react';
import { Rect, Text, Group } from 'react-konva';

const ImageElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const hasImage = element.src && element.src.trim() !== '';

  if (hasImage) {
    // TODO: Implement actual image rendering with Konva Image
    // For now, show a placeholder
  }

  return (
    <Group
      ref={ref}
      x={element.x + element.width / 2}
      y={element.y + element.height / 2}
      offsetX={element.width / 2}
      offsetY={element.height / 2}
      width={element.width}
      height={element.height}
      rotation={element.rotation || 0}
      opacity={element.opacity !== undefined ? element.opacity : 1}
      visible={element.visible !== false}
      draggable={!element.locked && !isPreviewMode}
      onClick={onClick}
      onDragEnd={onDragEnd}
    >
      {/* Placeholder rectangle */}
      <Rect
        x={-element.width / 2}
        y={-element.height / 2}
        width={element.width}
        height={element.height}
        fill={hasImage ? '#f0f0f0' : '#e8f4fd'}
        stroke="#ccc"
        strokeWidth={1}
        dash={hasImage ? [] : [5, 5]}
      />

      {/* Placeholder text */}
      <Text
        x={-element.width / 2}
        y={-element.height / 2}
        width={element.width}
        height={element.height}
        text={hasImage ? 'Loading...' : (element.placeholder || 'Image')}
        fontSize={12}
        fill="#666"
        align="center"
        verticalAlign="middle"
      />
    </Group>
  );
});

ImageElement.displayName = 'ImageElement';

export default ImageElement;
