import React from 'react';
import { useDispatch } from 'react-redux';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  TextFields as TextIcon,
  Image as ImageIcon,
  QrCode as QRCodeIcon,
  CropFree as BarcodeIcon,
  Crop75 as RectangleIcon,
  Circle as CircleIcon,
  Star as StarIcon,
  Person as PersonIcon,
  Business as LogoIcon,
} from '@mui/icons-material';

import { addElement } from '../../redux/idDesignerSlice';

const ToolPalette = () => {
  const dispatch = useDispatch();

  const handleAddElement = (elementType, properties = {}) => {
    const defaultProperties = {
      text: {
        type: 'text',
        text: 'Sample Text',
        fontSize: 16,
        fontFamily: 'Arial',
        color: '#000000',
        textAlign: 'left',
        width: 120,
        height: 30,
      },
      image: {
        type: 'image',
        src: null,
        width: 100,
        height: 100,
        placeholder: 'Image Placeholder',
      },
      photo: {
        type: 'image',
        src: null,
        width: 80,
        height: 100,
        placeholder: 'Photo Placeholder',
        isPhotoPlaceholder: true,
      },
      logo: {
        type: 'image',
        src: null,
        width: 60,
        height: 60,
        placeholder: 'Logo Placeholder',
        isLogoPlaceholder: true,
      },
      qrcode: {
        type: 'qrcode',
        data: '{{id}}',
        size: 80,
        width: 80,
        height: 80,
        errorCorrectionLevel: 'M',
      },
      barcode: {
        type: 'barcode',
        data: '{{id}}',
        format: 'CODE128',
        width: 120,
        height: 40,
      },
      rectangle: {
        type: 'shape',
        shapeType: 'rectangle',
        width: 100,
        height: 60,
        fill: '#e3f2fd',
        stroke: '#1976d2',
        strokeWidth: 1,
      },
      circle: {
        type: 'shape',
        shapeType: 'circle',
        width: 80,
        height: 80,
        fill: '#f3e5f5',
        stroke: '#7b1fa2',
        strokeWidth: 1,
      },
    };

    const elementProps = {
      ...defaultProperties[elementType],
      ...properties,
    };

    dispatch(addElement(elementProps));
  };

  const ToolButton = ({ icon, label, onClick, color = 'primary' }) => (
    <Tooltip title={label}>
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          cursor: 'pointer',
          textAlign: 'center',
          transition: 'all 0.2s',
          '&:hover': {
            elevation: 3,
            bgcolor: 'action.hover',
          },
        }}
        onClick={onClick}
      >
        <Box sx={{ color: `${color}.main`, mb: 0.5 }}>
          {icon}
        </Box>
        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
          {label}
        </Typography>
      </Paper>
    </Tooltip>
  );

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
          Design Tools
        </Typography>
      </Box>

      {/* Text Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Text Elements</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <ToolButton
                icon={<TextIcon />}
                label="Text"
                onClick={() => handleAddElement('text')}
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<TextIcon />}
                label="Dynamic Text"
                onClick={() => handleAddElement('text', { text: '{{name}}' })}
                color="secondary"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Image Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Images</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <ToolButton
                icon={<PersonIcon />}
                label="Photo"
                onClick={() => handleAddElement('photo')}
                color="info"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<LogoIcon />}
                label="Logo"
                onClick={() => handleAddElement('logo')}
                color="warning"
              />
            </Grid>
            <Grid item xs={12}>
              <ToolButton
                icon={<ImageIcon />}
                label="Image"
                onClick={() => handleAddElement('image')}
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Code Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Codes</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <ToolButton
                icon={<QRCodeIcon />}
                label="QR Code"
                onClick={() => handleAddElement('qrcode')}
                color="success"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<BarcodeIcon />}
                label="Barcode"
                onClick={() => handleAddElement('barcode')}
                color="success"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Shape Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Shapes</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <ToolButton
                icon={<RectangleIcon />}
                label="Rectangle"
                onClick={() => handleAddElement('rectangle')}
                color="error"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<CircleIcon />}
                label="Circle"
                onClick={() => handleAddElement('circle')}
                color="error"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      <Divider sx={{ my: 2 }} />

      {/* Quick Actions */}
      <Box sx={{ p: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Quick Actions
        </Typography>
        <Button
          fullWidth
          variant="outlined"
          size="small"
          sx={{ mb: 1 }}
          onClick={() => {
            // Add a complete ID card template
            const elements = [
              { type: 'text', text: '{{name}}', x: 20, y: 20, fontSize: 18, fontWeight: 'bold' },
              { type: 'text', text: 'ID: {{id}}', x: 20, y: 50, fontSize: 12 },
              { type: 'text', text: '{{department}}', x: 20, y: 70, fontSize: 12 },
              { type: 'image', x: 200, y: 20, width: 60, height: 75, isPhotoPlaceholder: true },
              { type: 'qrcode', data: '{{id}}', x: 20, y: 100, size: 60 },
            ];
            
            elements.forEach(element => {
              dispatch(addElement(element));
            });
          }}
        >
          Add ID Card Template
        </Button>
        
        <Button
          fullWidth
          variant="outlined"
          size="small"
          onClick={() => {
            // Add a visitor badge template
            const elements = [
              { type: 'text', text: 'VISITOR', x: 20, y: 20, fontSize: 16, fontWeight: 'bold', color: '#d32f2f' },
              { type: 'text', text: '{{name}}', x: 20, y: 50, fontSize: 14 },
              { type: 'text', text: 'Visiting: {{department}}', x: 20, y: 70, fontSize: 10 },
              { type: 'text', text: 'Date: {{date}}', x: 20, y: 85, fontSize: 10 },
              { type: 'image', x: 180, y: 20, width: 50, height: 60, isPhotoPlaceholder: true },
            ];
            
            elements.forEach(element => {
              dispatch(addElement(element));
            });
          }}
        >
          Add Visitor Badge Template
        </Button>
      </Box>
    </Box>
  );
};

export default ToolPalette;
